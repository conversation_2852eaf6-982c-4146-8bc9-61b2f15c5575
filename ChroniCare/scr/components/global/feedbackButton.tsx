import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { MessageSquarePlus } from 'lucide-react-native';
import { useTheme } from '../../context/themeContext';

interface FeedbackButtonProps {
  onPress: () => void;
}

const FeedbackButton: React.FC<FeedbackButtonProps> = ({ onPress }) => {
  const theme = useTheme();

  return (
    <TouchableOpacity
      style={[
        styles.button,
        { backgroundColor: theme.theme.colors.Primary.primary500 },
      ]}
      onPress={onPress}
    >
      <MessageSquarePlus color={theme.theme.colors.Text.text0} size={30} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    position: 'absolute',
    bottom: 90,
    right: 20,
    width: 50,
    height: 50,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
});

export default FeedbackButton;
