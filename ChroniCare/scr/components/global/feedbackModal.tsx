import React from 'react';
import { View, Text, Modal, StyleSheet, TouchableOpacity, TextInput } from 'react-native';
import { useTheme } from '../../context/themeContext';
import { X } from 'lucide-react-native';

interface FeedbackModalProps {
  visible: boolean;
  onClose: () => void;
}

const FeedbackModal: React.FC<FeedbackModalProps> = ({ visible, onClose }) => {
  const theme = useTheme();

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View style={[styles.modalView, { backgroundColor: theme.theme.colors.Background.background0 }]}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <X color={theme.theme.colors.Text.text900} size={24} />
          </TouchableOpacity>
          <Text style={[styles.modalTitle, { color: theme.theme.colors.Text.text900 }]}>Give us your Feedback</Text>
          <Text style={[styles.modalText, { color: theme.theme.colors.Text.text500 }]}>
            What's on your mind? We'd love to hear from you.
          </Text>
          <TextInput
                style={[styles.input, { 
                    backgroundColor: theme.theme.colors.Background.background200, 
                    color: theme.theme.colors.Text.text900,
                    borderColor: theme.theme.colors.Background.background500
                }]}
                placeholder="Share your thoughts..."
                placeholderTextColor={theme.theme.colors.Text.text500}
                multiline
          />
            <TouchableOpacity
                style={[styles.submitButton, { backgroundColor: theme.theme.colors.Primary.primary500 }]}
                onPress={() => {
                    onClose();
                }}
            >
                <Text style={[styles.submitButtonText, { color: theme.theme.colors.Text.text0 }]}>Submit</Text>
            </TouchableOpacity>

        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    margin: 20,
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '90%',
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    padding: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  modalText: {
    marginBottom: 20,
    textAlign: 'center',
    fontSize: 16,
  },
  input: {
      width: '100%',
      height: 150,
      borderWidth: 1,
      borderRadius: 10,
      padding: 10,
      textAlignVertical: 'top',
      marginBottom: 20
  },
  submitButton: {
      paddingVertical: 12,
      paddingHorizontal: 30,
      borderRadius: 25,
      elevation: 2,
  },
  submitButtonText: {
      fontSize: 16,
      fontWeight: 'bold'
  }
});

export default FeedbackModal;
